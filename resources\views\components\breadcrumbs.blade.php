@if((!function_exists('getSetting') || getSetting('enable_breadcrumbs', true)) && !empty($breadcrumbs))
<nav aria-label="breadcrumb" class="breadcrumb-nav">
    <ol class="breadcrumb">
        @foreach($breadcrumbs as $index => $breadcrumb)
            @if($loop->last)
                <li class="breadcrumb-item active" aria-current="page">{{ $breadcrumb['name'] }}</li>
            @else
                <li class="breadcrumb-item">
                    <a href="{{ $breadcrumb['url'] }}">{{ $breadcrumb['name'] }}</a>
                </li>
            @endif
        @endforeach
    </ol>
</nav>

@push('structured-data')
    @if(function_exists('generateStructuredData'))
        {!! generateStructuredData('breadcrumbs', $breadcrumbs) !!}
    @endif
@endpush
@endif
