<?php
/**
 * Manual Database Setup Script
 * 
 * This script helps set up the database when the Laravel installation process fails
 * due to MySQL PDO extension issues in XAMPP.
 */

echo "=== Manual Database Setup Script ===\n\n";

// Database configuration
$host = '127.0.0.1';
$port = '3306';
$dbname = 'trashmail_db';
$username = 'root';
$password = '';

echo "Step 1: Checking MySQL connection...\n";

// Check if we can connect to MySQL server (without specifying database)
try {
    $dsn_server = "mysql:host=$host;port=$port;charset=utf8mb4";
    $pdo_server = new PDO($dsn_server, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);
    echo "✓ MySQL server connection successful\n";
} catch (PDOException $e) {
    echo "✗ MySQL server connection failed: " . $e->getMessage() . "\n";
    echo "\nPlease ensure:\n";
    echo "1. MySQL service is running in XAMPP\n";
    echo "2. MySQL credentials are correct\n";
    echo "3. MySQL is listening on port 3306\n";
    exit(1);
}

echo "\nStep 2: Creating database if not exists...\n";

try {
    $pdo_server->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✓ Database '$dbname' created/verified\n";
} catch (PDOException $e) {
    echo "✗ Database creation failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nStep 3: Connecting to the database...\n";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    echo "✓ Database connection successful\n";
} catch (PDOException $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nStep 4: Importing SQL file...\n";

$sql_file = __DIR__ . '/database/data.sql';
if (!file_exists($sql_file)) {
    echo "✗ SQL file not found: $sql_file\n";
    exit(1);
}

$sql_content = file_get_contents($sql_file);
if ($sql_content === false) {
    echo "✗ Could not read SQL file\n";
    exit(1);
}

echo "✓ SQL file loaded (" . number_format(strlen($sql_content)) . " bytes)\n";

// Split SQL into statements
$statements = array_filter(array_map('trim', explode(';', $sql_content)));
$total_statements = count($statements);
$successful = 0;
$failed = 0;

echo "Processing $total_statements SQL statements...\n";

foreach ($statements as $index => $statement) {
    if (empty($statement) || preg_match('/^(\/\*|--|\s*$)/', $statement)) {
        continue;
    }
    
    try {
        $pdo->exec($statement . ';');
        $successful++;
        
        // Show progress every 50 statements
        if (($index + 1) % 50 == 0) {
            echo "Processed " . ($index + 1) . "/$total_statements statements...\n";
        }
    } catch (PDOException $e) {
        $failed++;
        echo "Warning: Statement failed - " . substr($statement, 0, 50) . "... Error: " . $e->getMessage() . "\n";
    }
}

echo "\nStep 5: Import completed!\n";
echo "✓ Successful statements: $successful\n";
if ($failed > 0) {
    echo "⚠ Failed statements: $failed\n";
}

echo "\nStep 6: Verifying database structure...\n";

// Check if key tables exist
$key_tables = ['admins', 'settings', 'languages', 'faqs', 'ads'];
$tables_exist = 0;

foreach ($key_tables as $table) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
        $count = $stmt->fetchColumn();
        echo "✓ Table '$table' exists with $count records\n";
        $tables_exist++;
    } catch (PDOException $e) {
        echo "✗ Table '$table' missing or inaccessible\n";
    }
}

if ($tables_exist == count($key_tables)) {
    echo "\n🎉 Database setup completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Update your .env file with the database settings:\n";
    echo "   DB_CONNECTION=mysql\n";
    echo "   DB_HOST=127.0.0.1\n";
    echo "   DB_PORT=3306\n";
    echo "   DB_DATABASE=$dbname\n";
    echo "   DB_USERNAME=$username\n";
    echo "   DB_PASSWORD=$password\n";
    echo "\n2. Continue with the Laravel installation process\n";
} else {
    echo "\n⚠ Database setup completed with issues. Some tables may be missing.\n";
    echo "Please check the error messages above and consider:\n";
    echo "1. Fixing XAMPP MySQL PDO extension issues\n";
    echo "2. Running the Laravel installation process again\n";
}

echo "\n=== Script completed ===\n";
?>
