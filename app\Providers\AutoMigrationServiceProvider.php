<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class AutoMigrationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only run auto-migration if system is installed and database is available
        if (env('SYSTEM_INSTALLED') == 1) {
            $this->autoRunSeoMigration();
        }
    }

    /**
     * Automatically run the SEO settings migration if needed
     */
    private function autoRunSeoMigration()
    {
        try {
            // Check if database connection is available
            DB::connection()->getPdo();
            
            // Check if the settings table exists
            if (!Schema::hasTable('settings')) {
                return;
            }

            // Check if SEO settings already exist
            $seoSettingsExist = DB::table('settings')
                ->whereIn('key', [
                    'sitemap_enabled',
                    'structured_data_enabled',
                    'robots_txt_content'
                ])
                ->exists();

            // If SEO settings don't exist, run the migration
            if (!$seoSettingsExist) {
                Log::info('Auto-running SEO settings migration...');

                Artisan::call('migrate', [
                    '--path' => 'database/migrations/2024_01_15_000001_add_seo_settings.php',
                    '--force' => true
                ]);

                Log::info('SEO settings migration completed successfully.');
            }

            // Check if sitemap plugin removal migration needs to run
            $sitemapPluginExists = DB::table('plugins')->where('unique_name', 'sitemap')->exists();

            if ($sitemapPluginExists) {
                Log::info('Auto-running sitemap plugin removal migration...');

                Artisan::call('migrate', [
                    '--path' => 'database/migrations/2025_01_23_000001_remove_sitemap_plugin.php',
                    '--force' => true
                ]);

                Log::info('Sitemap plugin removal migration completed successfully.');
            }

        } catch (\Exception $e) {
            // Silently fail if database is not available
            // This prevents the application from breaking
            Log::debug('Auto-migration skipped: ' . $e->getMessage());
        }
    }
}
