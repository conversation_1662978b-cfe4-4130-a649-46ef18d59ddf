@extends('admin.layouts.admin')

@section('content')
    <!-- Settings -->
    <div class="settings">
        @include('admin.partials.settings')
        <!-- Settings Content -->
        <div class="settings-content w-100">
            <div class="box">
                <div class="row row-cols-auto g-2 justify-content-between align-items-center mb-4">
                    <div class="col">
                        <h5 class="mb-0">{{ __('Robots.txt Management') }}</h5>
                        <p class="text-muted mb-0">{{ __('Configure your robots.txt file to control search engine crawling') }}</p>
                    </div>
                    <div class="col-auto">
                        <a href="{{ url('/robots.txt') }}" target="_blank" class="btn btn-outline-primary">
                            <i class="fa-solid fa-external-link mx-1"></i>
                            {{ __('View Current') }}
                        </a>
                    </div>
                </div>

                <form action="{{ route('admin.settings.robots.update') }}" method="POST">
                    @csrf
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="robots_content" class="form-label">{{ __('Robots.txt Content') }}</label>
                            <textarea name="robots_content" id="robots_content" class="form-control" rows="15"
                                placeholder="User-agent: *&#10;Allow: /&#10;&#10;Sitemap: {{ url('/sitemap.xml') }}"
                                required>{{ old('robots_content', $robotsContent) }}</textarea>
                            <div class="form-text">
                                {{ __('Define rules for search engine crawlers. Common directives include User-agent, Allow, Disallow, and Sitemap.') }}
                            </div>
                            @error('robots_content')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">{{ __('Common Examples:') }}</h6>
                                <ul class="mb-0">
                                    <li><strong>Allow all:</strong> User-agent: * / Allow: /</li>
                                    <li><strong>Block all:</strong> User-agent: * / Disallow: /</li>
                                    <li><strong>Block specific paths:</strong> Disallow: /admin/ / Disallow: /private/</li>
                                    <li><strong>Sitemap reference:</strong> Sitemap: {{ url('/sitemap.xml') }}</li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa-solid fa-save mx-1"></i>
                                    {{ __('Save & Deploy') }}
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="previewRobots()">
                                    <i class="fa-solid fa-eye mx-1"></i>
                                    {{ __('Preview') }}
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="resetToDefault()">
                                    <i class="fa-solid fa-refresh mx-1"></i>
                                    {{ __('Reset to Default') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Robots.txt Preview') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre id="previewContent" class="bg-light p-3 rounded"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function previewRobots() {
    const content = document.getElementById('robots_content').value;
    document.getElementById('previewContent').textContent = content;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

function resetToDefault() {
    if (confirm('{{ __("Are you sure you want to reset to default robots.txt content?") }}')) {
        document.getElementById('robots_content').value = `User-agent: *
Allow: /

Sitemap: {{ url('/sitemap.xml') }}`;
    }
}
</script>
@endpush