<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckBlogEnabled
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            // Check if the blog is enabled
            if (function_exists('getSetting') && !getSetting('enable_blog')) {
                // Or return a 404 response
                abort(404);
            }
        } catch (\Exception $e) {
            // If database is not available, allow access (assume blog is enabled)
            // This prevents 500 errors during route registration
        }

        return $next($request);
    }
}
