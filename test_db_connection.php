<?php
// Test database connection
echo "Testing database connection...\n";

// Check if PDO MySQL extension is loaded
if (!extension_loaded('pdo_mysql')) {
    echo "ERROR: PDO MySQL extension is not loaded!\n";
    echo "Available PDO drivers: " . implode(', ', PDO::getAvailableDrivers()) . "\n";
    exit(1);
}

// Test connection
try {
    $host = '127.0.0.1';
    $port = '3306';
    $dbname = 'trashmail_db';
    $username = 'root';
    $password = '';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "SUCCESS: Database connection established!\n";
    echo "MySQL version: " . $pdo->getAttribute(PDO::ATTR_SERVER_VERSION) . "\n";
    
} catch (PDOException $e) {
    echo "ERROR: Database connection failed!\n";
    echo "Error: " . $e->getMessage() . "\n";
    
    // If database doesn't exist, try to create it
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "Attempting to create database...\n";
        try {
            $dsn_no_db = "mysql:host=$host;port=$port;charset=utf8mb4";
            $pdo_create = new PDO($dsn_no_db, $username, $password);
            $pdo_create->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "SUCCESS: Database '$dbname' created!\n";
        } catch (PDOException $e2) {
            echo "ERROR: Could not create database: " . $e2->getMessage() . "\n";
        }
    }
}
?>
