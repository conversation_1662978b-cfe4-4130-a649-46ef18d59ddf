@extends('admin.layouts.admin')

@section('content')
    <!-- Settings -->
    <div class="settings">
        @include('admin.partials.settings')
        <!-- Settings Content -->
        <div class="settings-content w-100">
            <div class="box">
                <div class="row row-cols-auto g-2 justify-content-between align-items-center mb-4">
                    <div class="col">
                        <h5 class="mb-0">{{ __('SEO Testing & Validation') }}</h5>
                        <p class="text-muted mb-0">{{ __('Test and validate your SEO implementation') }}</p>
                    </div>
                    <div class="col-auto">
                        <button type="button" class="btn btn-success" onclick="runQuickTest()">
                            <i class="fa-solid fa-bolt mx-1"></i>
                            {{ __('Quick Test') }}
                        </button>
                    </div>
                </div>

                <!-- Test Form -->
                <form id="seoTestForm" onsubmit="runCustomTest(event)">
                    <div class="row g-3 mb-4">
                        <div class="col-md-8">
                            <label for="test_url" class="form-label">{{ __('Test URL (optional)') }}</label>
                            <input type="url" id="test_url" name="test_url" class="form-control" 
                                placeholder="{{ url('/') }}" value="{{ url('/') }}">
                            <div class="form-text">{{ __('Leave empty to test the homepage') }}</div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">{{ __('Tests to Run') }}</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tests[]" value="structured_data" id="test_structured_data" checked>
                                <label class="form-check-label" for="test_structured_data">{{ __('Structured Data') }}</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tests[]" value="sitemap" id="test_sitemap" checked>
                                <label class="form-check-label" for="test_sitemap">{{ __('Sitemap') }}</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tests[]" value="robots" id="test_robots" checked>
                                <label class="form-check-label" for="test_robots">{{ __('Robots.txt') }}</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="tests[]" value="meta_tags" id="test_meta_tags" checked>
                                <label class="form-check-label" for="test_meta_tags">{{ __('Meta Tags') }}</label>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa-solid fa-play mx-1"></i>
                        {{ __('Run Tests') }}
                    </button>
                </form>

                <!-- Loading Indicator -->
                <div id="loadingIndicator" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">{{ __('Loading...') }}</span>
                    </div>
                    <p class="mt-2">{{ __('Running SEO tests...') }}</p>
                </div>

                <!-- Results Container -->
                <div id="testResults" class="mt-4" style="display: none;">
                    <h6 class="border-bottom pb-2 mb-3">{{ __('Test Results') }}</h6>
                    <div id="resultsContent"></div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function runQuickTest() {
    showLoading();
    
    fetch('{{ route("admin.settings.seo-test.quick") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        displayResults(data);
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        alert('{{ __("An error occurred while running the tests") }}');
    });
}

function runCustomTest(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
        test_url: formData.get('test_url'),
        tests: formData.getAll('tests[]')
    };
    
    if (data.tests.length === 0) {
        alert('{{ __("Please select at least one test to run") }}');
        return;
    }
    
    showLoading();
    
    fetch('{{ route("admin.settings.seo-test.run") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        displayResults(data);
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        alert('{{ __("An error occurred while running the tests") }}');
    });
}

function showLoading() {
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('testResults').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
}

function displayResults(data) {
    const resultsContainer = document.getElementById('testResults');
    const resultsContent = document.getElementById('resultsContent');
    
    let html = '';
    
    // Overall score (if available)
    if (data.score !== undefined) {
        const scoreClass = data.score >= 80 ? 'success' : data.score >= 60 ? 'warning' : 'danger';
        html += `
            <div class="alert alert-${scoreClass} mb-4">
                <h6 class="alert-heading">{{ __('Overall SEO Score') }}: ${data.score}%</h6>
                <p class="mb-0">${data.passed_tests} {{ __('out of') }} ${data.total_tests} {{ __('tests passed') }}</p>
            </div>
        `;
    }
    
    // Test results
    for (const [category, tests] of Object.entries(data.results)) {
        html += `<div class="card mb-3">`;
        html += `<div class="card-header"><h6 class="mb-0">${getCategoryTitle(category)}</h6></div>`;
        html += `<div class="card-body">`;
        
        for (const [testName, result] of Object.entries(tests)) {
            const statusClass = getStatusClass(result.status);
            const statusIcon = getStatusIcon(result.status);
            
            html += `
                <div class="d-flex align-items-center mb-2">
                    <i class="fa-solid ${statusIcon} text-${statusClass} me-2"></i>
                    <strong>${getTestTitle(testName)}:</strong>
                    <span class="ms-2">${result.message}</span>
                </div>
            `;
        }
        
        html += `</div></div>`;
    }
    
    // Test info
    html += `
        <div class="text-muted small mt-3">
            <p><strong>{{ __('Tested URL') }}:</strong> ${data.tested_url || '{{ url("/") }}'}</p>
            <p><strong>{{ __('Test Time') }}:</strong> ${new Date(data.timestamp).toLocaleString()}</p>
        </div>
    `;
    
    resultsContent.innerHTML = html;
    resultsContainer.style.display = 'block';
}

function getCategoryTitle(category) {
    const titles = {
        'structured_data': '{{ __("Structured Data") }}',
        'sitemap': '{{ __("Sitemap") }}',
        'robots': '{{ __("Robots.txt") }}',
        'meta_tags': '{{ __("Meta Tags") }}'
    };
    return titles[category] || category;
}

function getTestTitle(testName) {
    const titles = {
        'json_ld_syntax': '{{ __("JSON-LD Syntax") }}',
        'google_rich_results': '{{ __("Google Rich Results") }}',
        'sitemap_accessibility': '{{ __("Sitemap Accessibility") }}',
        'sitemap_syntax': '{{ __("XML Syntax") }}',
        'sitemap_urls': '{{ __("URL Count") }}',
        'sitemap_structure': '{{ __("Structure") }}',
        'robots_accessibility': '{{ __("Accessibility") }}',
        'robots_syntax': '{{ __("Syntax") }}',
        'robots_sitemap': '{{ __("Sitemap Reference") }}',
        'meta_title': '{{ __("Title Tag") }}',
        'meta_description': '{{ __("Meta Description") }}',
        'meta_canonical': '{{ __("Canonical URL") }}',
        'meta_og': '{{ __("OpenGraph Tags") }}',
        'meta_twitter': '{{ __("Twitter Cards") }}',
        'page_accessibility': '{{ __("Page Accessibility") }}'
    };
    return titles[testName] || testName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function getStatusClass(status) {
    const classes = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
    };
    return classes[status] || 'secondary';
}

function getStatusIcon(status) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-times-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return icons[status] || 'fa-question-circle';
}
</script>
@endpush
