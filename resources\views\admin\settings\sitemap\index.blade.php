@extends('admin.layouts.admin')

@section('content')
    <!-- Settings -->
    <div class="settings">
        @include('admin.partials.settings')
        <!-- Settings Content -->
        <div class="settings-content w-100">
            <div class="box">
                <div class="row row-cols-auto g-2 justify-content-between align-items-center mb-4">
                    <div class="col">
                        <h5 class="mb-0">{{ __('Sitemap Management') }}</h5>
                        <p class="text-muted mb-0">{{ __('Configure automatic sitemap generation for better search engine indexing') }}</p>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <a href="{{ url('/sitemap.xml') }}" target="_blank" class="btn btn-outline-primary">
                                <i class="fa-solid fa-external-link mx-1"></i>
                                {{ __('View Sitemap') }}
                            </a>
                            <form action="{{ route('admin.settings.sitemap.generate') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-success">
                                    <i class="fa-solid fa-refresh mx-1"></i>
                                    {{ __('Generate Now') }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sitemap URL Display -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title mb-3">{{ __('Sitemap URL') }}</h6>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="sitemapUrl" value="{{ url('/sitemap.xml') }}" readonly>
                                    <button class="btn btn-outline-secondary" type="button" id="copySitemapUrl" title="{{ __('Copy to clipboard') }}">
                                        <i class="fa-solid fa-copy"></i>
                                    </button>
                                </div>
                                <small class="form-text text-muted mt-2">
                                    {{ __('Use this URL to submit your sitemap to search engines like Google Search Console and Bing Webmaster Tools.') }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="row g-3 mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4 class="mb-1">{{ $stats['total_pages'] }}</h4>
                                <small>{{ __('Total Pages') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4 class="mb-1">{{ $stats['total_blog_posts'] }}</h4>
                                <small>{{ __('Blog Posts') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h6 class="mb-1">{{ __('Last Generated') }}</h6>
                                <small>{{ $stats['last_generated'] ? $stats['last_generated']->diffForHumans() : __('Never') }}</small>
                            </div>
                        </div>
                    </div>
                </div>

                <form action="{{ route('admin.settings.sitemap.update') }}" method="POST">
                    @csrf
                    <div class="row g-3">
                        <!-- General Settings -->
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('General Settings') }}</h6>
                        </div>

                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="sitemap_enabled" id="sitemap_enabled"
                                    value="1" {{ $settings['sitemap_enabled'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="sitemap_enabled">
                                    {{ __('Enable Sitemap Generation') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('Enable or disable automatic sitemap generation') }}</div>
                        </div>

                        <div class="col-md-6">
                            <label for="sitemap_cache_duration" class="form-label">{{ __('Cache Duration (minutes)') }}</label>
                            <input type="number" name="sitemap_cache_duration" id="sitemap_cache_duration"
                                class="form-control" value="{{ old('sitemap_cache_duration', $settings['sitemap_cache_duration']) }}"
                                min="1" max="1440" required>
                            <div class="form-text">{{ __('How long to cache the sitemap (1-1440 minutes)') }}</div>
                        </div>

                        <!-- Content Inclusion -->
                        <div class="col-12 mt-4">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('Content Inclusion') }}</h6>
                        </div>

                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="sitemap_include_pages" id="sitemap_include_pages"
                                    value="1" {{ $settings['sitemap_include_pages'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="sitemap_include_pages">
                                    {{ __('Include Pages') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('Include static pages in sitemap') }}</div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="sitemap_include_blog" id="sitemap_include_blog"
                                    value="1" {{ $settings['sitemap_include_blog'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="sitemap_include_blog">
                                    {{ __('Include Blog Posts') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('Include blog posts in sitemap') }}</div>
                        </div>

                        <!-- Priority Settings -->
                        <div class="col-12 mt-4">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('Priority Settings') }}</h6>
                        </div>

                        <div class="col-md-6">
                            <label for="sitemap_pages_priority" class="form-label">{{ __('Pages Priority') }}</label>
                            <input type="number" name="sitemap_pages_priority" id="sitemap_pages_priority"
                                class="form-control" value="{{ old('sitemap_pages_priority', $settings['sitemap_pages_priority']) }}"
                                min="0" max="1" step="0.1" required>
                            <div class="form-text">{{ __('Priority for static pages (0.0 - 1.0)') }}</div>
                        </div>

                        <div class="col-md-6">
                            <label for="sitemap_blog_priority" class="form-label">{{ __('Blog Priority') }}</label>
                            <input type="number" name="sitemap_blog_priority" id="sitemap_blog_priority"
                                class="form-control" value="{{ old('sitemap_blog_priority', $settings['sitemap_blog_priority']) }}"
                                min="0" max="1" step="0.1" required>
                            <div class="form-text">{{ __('Priority for blog posts (0.0 - 1.0)') }}</div>
                        </div>

                        <!-- Change Frequency Settings -->
                        <div class="col-12 mt-4">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('Change Frequency') }}</h6>
                        </div>

                        <div class="col-md-6">
                            <label for="sitemap_pages_changefreq" class="form-label">{{ __('Pages Change Frequency') }}</label>
                            <select name="sitemap_pages_changefreq" id="sitemap_pages_changefreq" class="form-select" required>
                                <option value="always" {{ $settings['sitemap_pages_changefreq'] == 'always' ? 'selected' : '' }}>{{ __('Always') }}</option>
                                <option value="hourly" {{ $settings['sitemap_pages_changefreq'] == 'hourly' ? 'selected' : '' }}>{{ __('Hourly') }}</option>
                                <option value="daily" {{ $settings['sitemap_pages_changefreq'] == 'daily' ? 'selected' : '' }}>{{ __('Daily') }}</option>
                                <option value="weekly" {{ $settings['sitemap_pages_changefreq'] == 'weekly' ? 'selected' : '' }}>{{ __('Weekly') }}</option>
                                <option value="monthly" {{ $settings['sitemap_pages_changefreq'] == 'monthly' ? 'selected' : '' }}>{{ __('Monthly') }}</option>
                                <option value="yearly" {{ $settings['sitemap_pages_changefreq'] == 'yearly' ? 'selected' : '' }}>{{ __('Yearly') }}</option>
                                <option value="never" {{ $settings['sitemap_pages_changefreq'] == 'never' ? 'selected' : '' }}>{{ __('Never') }}</option>
                            </select>
                            <div class="form-text">{{ __('How frequently pages are likely to change') }}</div>
                        </div>

                        <div class="col-md-6">
                            <label for="sitemap_blog_changefreq" class="form-label">{{ __('Blog Change Frequency') }}</label>
                            <select name="sitemap_blog_changefreq" id="sitemap_blog_changefreq" class="form-select" required>
                                <option value="always" {{ $settings['sitemap_blog_changefreq'] == 'always' ? 'selected' : '' }}>{{ __('Always') }}</option>
                                <option value="hourly" {{ $settings['sitemap_blog_changefreq'] == 'hourly' ? 'selected' : '' }}>{{ __('Hourly') }}</option>
                                <option value="daily" {{ $settings['sitemap_blog_changefreq'] == 'daily' ? 'selected' : '' }}>{{ __('Daily') }}</option>
                                <option value="weekly" {{ $settings['sitemap_blog_changefreq'] == 'weekly' ? 'selected' : '' }}>{{ __('Weekly') }}</option>
                                <option value="monthly" {{ $settings['sitemap_blog_changefreq'] == 'monthly' ? 'selected' : '' }}>{{ __('Monthly') }}</option>
                                <option value="yearly" {{ $settings['sitemap_blog_changefreq'] == 'yearly' ? 'selected' : '' }}>{{ __('Yearly') }}</option>
                                <option value="never" {{ $settings['sitemap_blog_changefreq'] == 'never' ? 'selected' : '' }}>{{ __('Never') }}</option>
                            </select>
                            <div class="form-text">{{ __('How frequently blog posts are likely to change') }}</div>
                        </div>

                        <div class="col-12 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-solid fa-save mx-1"></i>
                                {{ __('Save Settings') }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const copySitemapUrlBtn = document.getElementById('copySitemapUrl');
    const sitemapUrlInput = document.getElementById('sitemapUrl');

    if (copySitemapUrlBtn && sitemapUrlInput) {
        copySitemapUrlBtn.addEventListener('click', function() {
            sitemapUrlInput.select();
            sitemapUrlInput.setSelectionRange(0, 99999); // For mobile devices

            try {
                document.execCommand('copy');

                // Change button icon and text temporarily
                const originalHTML = copySitemapUrlBtn.innerHTML;
                copySitemapUrlBtn.innerHTML = '<i class="fa-solid fa-check text-success"></i>';
                copySitemapUrlBtn.classList.add('btn-success');
                copySitemapUrlBtn.classList.remove('btn-outline-secondary');

                setTimeout(function() {
                    copySitemapUrlBtn.innerHTML = originalHTML;
                    copySitemapUrlBtn.classList.remove('btn-success');
                    copySitemapUrlBtn.classList.add('btn-outline-secondary');
                }, 2000);

            } catch (err) {
                console.error('Failed to copy: ', err);
                alert('{{ __("Failed to copy to clipboard") }}');
            }
        });
    }
});
</script>
@endpush