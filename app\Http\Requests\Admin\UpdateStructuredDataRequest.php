<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateStructuredDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'structured_data_enabled' => 'required|boolean',
            'structured_data_organization' => 'required|boolean',
            'structured_data_website' => 'required|boolean',
            'structured_data_breadcrumbs' => 'required|boolean',
            'structured_data_articles' => 'required|boolean',
            'organization_name' => 'required|string|max:255',
            'organization_logo' => 'nullable|string|max:500|url',
            'organization_url' => 'required|url|max:500',
            'organization_description' => 'nullable|string|max:1000',
            'organization_contact_type' => 'nullable|string|max:100|in:customer service,technical support,billing support,bill payment,sales,reservations,credit card support,emergency,baggage tracking,roadside assistance,package tracking',
            'organization_telephone' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'organization_email' => 'nullable|email|max:255',
            'organization_address_street' => 'nullable|string|max:255',
            'organization_address_city' => 'nullable|string|max:100',
            'organization_address_region' => 'nullable|string|max:100',
            'organization_address_postal' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[A-Za-z0-9\s\-]+$/'
            ],
            'organization_address_country' => 'nullable|string|max:100',
            'social_media_facebook' => 'nullable|url|max:500|regex:/^https?:\/\/(www\.)?facebook\.com\/.*/',
            'social_media_twitter' => 'nullable|url|max:500|regex:/^https?:\/\/(www\.)?(twitter\.com|x\.com)\/.*/',
            'social_media_instagram' => 'nullable|url|max:500|regex:/^https?:\/\/(www\.)?instagram\.com\/.*/',
            'social_media_linkedin' => 'nullable|url|max:500|regex:/^https?:\/\/(www\.)?linkedin\.com\/.*/',
            'social_media_youtube' => 'nullable|url|max:500|regex:/^https?:\/\/(www\.)?youtube\.com\/.*/',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'organization_name.required' => 'Organization name is required.',
            'organization_url.required' => 'Organization URL is required.',
            'organization_url.url' => 'Organization URL must be a valid URL.',
            'organization_logo.url' => 'Organization logo must be a valid URL.',
            'organization_contact_type.in' => 'Invalid contact type selected.',
            'organization_telephone.regex' => 'Telephone number format is invalid.',
            'organization_address_postal.regex' => 'Postal code format is invalid.',
            'social_media_facebook.regex' => 'Facebook URL must be a valid Facebook profile or page URL.',
            'social_media_twitter.regex' => 'Twitter URL must be a valid Twitter or X.com profile URL.',
            'social_media_instagram.regex' => 'Instagram URL must be a valid Instagram profile URL.',
            'social_media_linkedin.regex' => 'LinkedIn URL must be a valid LinkedIn profile or company URL.',
            'social_media_youtube.regex' => 'YouTube URL must be a valid YouTube channel URL.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert checkbox values to boolean
        $this->merge([
            'structured_data_enabled' => $this->boolean('structured_data_enabled'),
            'structured_data_organization' => $this->boolean('structured_data_organization'),
            'structured_data_website' => $this->boolean('structured_data_website'),
            'structured_data_breadcrumbs' => $this->boolean('structured_data_breadcrumbs'),
            'structured_data_articles' => $this->boolean('structured_data_articles'),
        ]);
    }
}
