@extends('admin.layouts.admin')

@section('content')
    <!-- Settings -->
    <div class="settings">
        @include('admin.partials.settings')
        <!-- Settings Content -->
        <div class="settings-content w-100">
            <div class="box">
                <div class="row row-cols-auto g-2 justify-content-between align-items-center mb-4">
                    <div class="col">
                        <h5 class="mb-0">{{ __('Structured Data Management') }}</h5>
                        <p class="text-muted mb-0">{{ __('Configure JSON-LD structured data for better search engine understanding') }}</p>
                    </div>
                    <div class="col-auto">
                        <button type="button" class="btn btn-outline-info" onclick="previewStructuredData()">
                            <i class="fa-solid fa-eye mx-1"></i>
                            {{ __('Preview JSON-LD') }}
                        </button>
                    </div>
                </div>

                <form action="{{ route('admin.settings.structured-data.update') }}" method="POST">
                    @csrf
                    <div class="row g-3">
                        <!-- General Settings -->
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('General Settings') }}</h6>
                        </div>

                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_enabled" id="structured_data_enabled" 
                                    value="1" {{ $settings['structured_data_enabled'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_enabled">
                                    {{ __('Enable Structured Data') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('Enable or disable JSON-LD structured data output') }}</div>
                        </div>

                        <!-- Schema Types -->
                        <div class="col-12 mt-4">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('Schema Types') }}</h6>
                        </div>

                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_organization" id="structured_data_organization" 
                                    value="1" {{ $settings['structured_data_organization'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_organization">
                                    {{ __('Organization') }}
                                </label>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_website" id="structured_data_website" 
                                    value="1" {{ $settings['structured_data_website'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_website">
                                    {{ __('Website') }}
                                </label>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_breadcrumbs" id="structured_data_breadcrumbs" 
                                    value="1" {{ $settings['structured_data_breadcrumbs'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_breadcrumbs">
                                    {{ __('Breadcrumbs') }}
                                </label>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_articles" id="structured_data_articles"
                                    value="1" {{ $settings['structured_data_articles'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_articles">
                                    {{ __('Articles') }}
                                </label>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_service" id="structured_data_service"
                                    value="1" {{ $settings['structured_data_service'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_service">
                                    {{ __('Service') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('Email service schema') }}</div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_software" id="structured_data_software"
                                    value="1" {{ $settings['structured_data_software'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_software">
                                    {{ __('Software App') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('Web application schema') }}</div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_faq" id="structured_data_faq"
                                    value="1" {{ $settings['structured_data_faq'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_faq">
                                    {{ __('FAQ') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('FAQ page schema') }}</div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_product" id="structured_data_product"
                                    value="1" {{ $settings['structured_data_product'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_product">
                                    {{ __('Product') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('Service as product schema') }}</div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_howto" id="structured_data_howto"
                                    value="1" {{ $settings['structured_data_howto'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_howto">
                                    {{ __('How-To Guides') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('Step-by-step guides schema') }}</div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="structured_data_localbusiness" id="structured_data_localbusiness"
                                    value="1" {{ $settings['structured_data_localbusiness'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="structured_data_localbusiness">
                                    {{ __('Local Business') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('Local business schema (if applicable)') }}</div>
                        </div>

                        <!-- Organization Information -->
                        <div class="col-12 mt-4">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('Organization Information') }}</h6>
                        </div>

                        <div class="col-md-6">
                            <label for="organization_name" class="form-label">{{ __('Organization Name') }}</label>
                            <input type="text" name="organization_name" id="organization_name" class="form-control" 
                                value="{{ old('organization_name', $settings['organization_name']) }}" required>
                        </div>

                        <div class="col-md-6">
                            <label for="organization_url" class="form-label">{{ __('Organization URL') }}</label>
                            <input type="url" name="organization_url" id="organization_url" class="form-control" 
                                value="{{ old('organization_url', $settings['organization_url']) }}" required>
                        </div>

                        <div class="col-md-6">
                            <label for="organization_logo" class="form-label">{{ __('Organization Logo URL') }}</label>
                            <input type="text" name="organization_logo" id="organization_logo" class="form-control" 
                                value="{{ old('organization_logo', $settings['organization_logo']) }}">
                            <div class="form-text">{{ __('Full URL to your organization logo') }}</div>
                        </div>

                        <div class="col-md-6">
                            <label for="organization_contact_type" class="form-label">{{ __('Contact Type') }}</label>
                            <select name="organization_contact_type" id="organization_contact_type" class="form-select">
                                <option value="customer service" {{ $settings['organization_contact_type'] == 'customer service' ? 'selected' : '' }}>{{ __('Customer Service') }}</option>
                                <option value="technical support" {{ $settings['organization_contact_type'] == 'technical support' ? 'selected' : '' }}>{{ __('Technical Support') }}</option>
                                <option value="billing support" {{ $settings['organization_contact_type'] == 'billing support' ? 'selected' : '' }}>{{ __('Billing Support') }}</option>
                                <option value="bill payment" {{ $settings['organization_contact_type'] == 'bill payment' ? 'selected' : '' }}>{{ __('Bill Payment') }}</option>
                                <option value="sales" {{ $settings['organization_contact_type'] == 'sales' ? 'selected' : '' }}>{{ __('Sales') }}</option>
                                <option value="reservations" {{ $settings['organization_contact_type'] == 'reservations' ? 'selected' : '' }}>{{ __('Reservations') }}</option>
                                <option value="credit card support" {{ $settings['organization_contact_type'] == 'credit card support' ? 'selected' : '' }}>{{ __('Credit Card Support') }}</option>
                                <option value="emergency" {{ $settings['organization_contact_type'] == 'emergency' ? 'selected' : '' }}>{{ __('Emergency') }}</option>
                                <option value="baggage tracking" {{ $settings['organization_contact_type'] == 'baggage tracking' ? 'selected' : '' }}>{{ __('Baggage Tracking') }}</option>
                                <option value="roadside assistance" {{ $settings['organization_contact_type'] == 'roadside assistance' ? 'selected' : '' }}>{{ __('Roadside Assistance') }}</option>
                                <option value="package tracking" {{ $settings['organization_contact_type'] == 'package tracking' ? 'selected' : '' }}>{{ __('Package Tracking') }}</option>
                            </select>
                        </div>

                        <div class="col-12">
                            <label for="organization_description" class="form-label">{{ __('Organization Description') }}</label>
                            <textarea name="organization_description" id="organization_description" class="form-control" rows="3">{{ old('organization_description', $settings['organization_description']) }}</textarea>
                            <div class="form-text">{{ __('Brief description of your organization') }}</div>
                        </div>

                        <!-- Contact Information -->
                        <div class="col-12 mt-4">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('Contact Information') }}</h6>
                        </div>

                        <div class="col-md-6">
                            <label for="organization_telephone" class="form-label">{{ __('Telephone') }}</label>
                            <input type="tel" name="organization_telephone" id="organization_telephone" class="form-control" 
                                value="{{ old('organization_telephone', $settings['organization_telephone']) }}">
                        </div>

                        <div class="col-md-6">
                            <label for="organization_email" class="form-label">{{ __('Email') }}</label>
                            <input type="email" name="organization_email" id="organization_email" class="form-control" 
                                value="{{ old('organization_email', $settings['organization_email']) }}">
                        </div>

                        <!-- Address Information -->
                        <div class="col-12 mt-4">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('Address Information') }}</h6>
                        </div>

                        <div class="col-md-6">
                            <label for="organization_address_street" class="form-label">{{ __('Street Address') }}</label>
                            <input type="text" name="organization_address_street" id="organization_address_street" class="form-control" 
                                value="{{ old('organization_address_street', $settings['organization_address_street']) }}">
                        </div>

                        <div class="col-md-6">
                            <label for="organization_address_city" class="form-label">{{ __('City') }}</label>
                            <input type="text" name="organization_address_city" id="organization_address_city" class="form-control" 
                                value="{{ old('organization_address_city', $settings['organization_address_city']) }}">
                        </div>

                        <div class="col-md-4">
                            <label for="organization_address_region" class="form-label">{{ __('State/Region') }}</label>
                            <input type="text" name="organization_address_region" id="organization_address_region" class="form-control" 
                                value="{{ old('organization_address_region', $settings['organization_address_region']) }}">
                        </div>

                        <div class="col-md-4">
                            <label for="organization_address_postal" class="form-label">{{ __('Postal Code') }}</label>
                            <input type="text" name="organization_address_postal" id="organization_address_postal" class="form-control" 
                                value="{{ old('organization_address_postal', $settings['organization_address_postal']) }}">
                        </div>

                        <div class="col-md-4">
                            <label for="organization_address_country" class="form-label">{{ __('Country') }}</label>
                            <input type="text" name="organization_address_country" id="organization_address_country" class="form-control" 
                                value="{{ old('organization_address_country', $settings['organization_address_country']) }}">
                        </div>

                        <!-- Social Media Links -->
                        <div class="col-12 mt-4">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('Social Media Links') }}</h6>
                        </div>

                        <div class="col-md-6">
                            <label for="social_media_facebook" class="form-label">{{ __('Facebook URL') }}</label>
                            <input type="url" name="social_media_facebook" id="social_media_facebook" class="form-control" 
                                value="{{ old('social_media_facebook', $settings['social_media_facebook']) }}">
                        </div>

                        <div class="col-md-6">
                            <label for="social_media_twitter" class="form-label">{{ __('Twitter URL') }}</label>
                            <input type="url" name="social_media_twitter" id="social_media_twitter" class="form-control" 
                                value="{{ old('social_media_twitter', $settings['social_media_twitter']) }}">
                        </div>

                        <div class="col-md-6">
                            <label for="social_media_instagram" class="form-label">{{ __('Instagram URL') }}</label>
                            <input type="url" name="social_media_instagram" id="social_media_instagram" class="form-control" 
                                value="{{ old('social_media_instagram', $settings['social_media_instagram']) }}">
                        </div>

                        <div class="col-md-6">
                            <label for="social_media_linkedin" class="form-label">{{ __('LinkedIn URL') }}</label>
                            <input type="url" name="social_media_linkedin" id="social_media_linkedin" class="form-control" 
                                value="{{ old('social_media_linkedin', $settings['social_media_linkedin']) }}">
                        </div>

                        <div class="col-md-6">
                            <label for="social_media_youtube" class="form-label">{{ __('YouTube URL') }}</label>
                            <input type="url" name="social_media_youtube" id="social_media_youtube" class="form-control"
                                value="{{ old('social_media_youtube', $settings['social_media_youtube']) }}">
                        </div>

                        <!-- Service Settings -->
                        <div class="col-12 mt-4">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('Service Settings') }}</h6>
                        </div>

                        <div class="col-md-6">
                            <label for="service_name" class="form-label">{{ __('Service Name') }}</label>
                            <input type="text" name="service_name" id="service_name" class="form-control"
                                value="{{ old('service_name', $settings['service_name']) }}">
                            <div class="form-text">{{ __('Name of your email service') }}</div>
                        </div>

                        <div class="col-md-6">
                            <label for="service_area_served" class="form-label">{{ __('Area Served') }}</label>
                            <input type="text" name="service_area_served" id="service_area_served" class="form-control"
                                value="{{ old('service_area_served', $settings['service_area_served']) }}">
                            <div class="form-text">{{ __('Geographic area served (e.g., Worldwide, USA, Europe)') }}</div>
                        </div>

                        <div class="col-12">
                            <label for="service_description" class="form-label">{{ __('Service Description') }}</label>
                            <textarea name="service_description" id="service_description" class="form-control" rows="3">{{ old('service_description', $settings['service_description']) }}</textarea>
                            <div class="form-text">{{ __('Detailed description of your email service') }}</div>
                        </div>

                        <!-- Service Features -->
                        <div class="col-12 mt-3">
                            <label class="form-label">{{ __('Service Features') }}</label>
                            <div class="row g-2">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_feature_privacy" id="service_feature_privacy"
                                            value="1" {{ $settings['service_feature_privacy'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="service_feature_privacy">
                                            {{ __('Privacy Protection') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_feature_temporary" id="service_feature_temporary"
                                            value="1" {{ $settings['service_feature_temporary'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="service_feature_temporary">
                                            {{ __('Temporary Emails') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_feature_anonymous" id="service_feature_anonymous"
                                            value="1" {{ $settings['service_feature_anonymous'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="service_feature_anonymous">
                                            {{ __('Anonymous Email') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_feature_spam_protection" id="service_feature_spam_protection"
                                            value="1" {{ $settings['service_feature_spam_protection'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="service_feature_spam_protection">
                                            {{ __('Spam Protection') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Application Settings -->
                        <div class="col-12 mt-4">
                            <h6 class="border-bottom pb-2 mb-3">{{ __('Application Settings') }}</h6>
                        </div>

                        <div class="col-md-4">
                            <label for="app_name" class="form-label">{{ __('App Name') }}</label>
                            <input type="text" name="app_name" id="app_name" class="form-control"
                                value="{{ old('app_name', $settings['app_name']) }}">
                        </div>

                        <div class="col-md-4">
                            <label for="app_version" class="form-label">{{ __('App Version') }}</label>
                            <input type="text" name="app_version" id="app_version" class="form-control"
                                value="{{ old('app_version', $settings['app_version']) }}">
                        </div>

                        <div class="col-md-4">
                            <div class="form-check form-switch mt-4">
                                <input class="form-check-input" type="checkbox" name="enable_domain_search" id="enable_domain_search"
                                    value="1" {{ $settings['enable_domain_search'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="enable_domain_search">
                                    {{ __('Enable Domain Search') }}
                                </label>
                            </div>
                        </div>

                        <div class="col-12">
                            <label for="app_description" class="form-label">{{ __('App Description') }}</label>
                            <textarea name="app_description" id="app_description" class="form-control" rows="3">{{ old('app_description', $settings['app_description']) }}</textarea>
                            <div class="form-text">{{ __('Description of your web application') }}</div>
                        </div>

                        <div class="col-12 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-solid fa-save mx-1"></i>
                                {{ __('Save Settings') }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Structured Data Preview') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre id="previewContent" class="bg-light p-3 rounded" style="max-height: 500px; overflow-y: auto; color: #333 !important; background-color: #f8f9fa !important;"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                    <a href="https://search.google.com/test/rich-results" target="_blank" class="btn btn-primary">
                        {{ __('Test with Google') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function previewStructuredData() {
    fetch('{{ route("admin.settings.structured-data.preview") }}')
        .then(response => response.json())
        .then(data => {
            document.getElementById('previewContent').textContent = JSON.stringify(data, null, 2);
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ __("Failed to load preview") }}');
        });
}
</script>
@endpush
