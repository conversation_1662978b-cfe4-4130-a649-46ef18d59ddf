<?php

namespace App\Http\Requests\Admin;

use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $admin = Auth::guard('admin')->user();
        return [
            'firstname' => "required|max:190",
            'lastname' => "required|max:190",
            'email' => "required|unique:admins,email," . $admin->id,
            'avatar' => "nullable|image|mimes:png,jpg,jpeg,webp|max:2048",
        ];
    }
}
