<?php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Models\BlogPost;
use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;

class SitemapController extends Controller
{
    public function index()
    {
        try {
            // Check if sitemap is enabled
            try {
                if (function_exists('getSetting') && !getSetting('sitemap_enabled', true)) {
                    abort(404);
                }
            } catch (\Exception $e) {
                // If getSetting fails, continue with sitemap generation
                \Log::warning('Failed to check sitemap_enabled setting: ' . $e->getMessage());
            }

            // Check if database is available
            $databaseAvailable = $this->isDatabaseAvailable();

            $cacheKey = 'sitemap_xml';
            $cacheDuration = 60; // Default cache duration

            try {
                $cacheDuration = function_exists('getSetting') ? getSetting('sitemap_cache_duration', 60) : 60;
            } catch (\Exception $e) {
                \Log::warning('Failed to get sitemap cache duration: ' . $e->getMessage());
            }

            $sitemap = '';

            try {
                $sitemap = Cache::remember($cacheKey, now()->addMinutes($cacheDuration), function () {
                    return $this->generateSitemap();
                });
                Cache::put('sitemap_last_generated', now());
            } catch (\Exception $e) {
                \Log::error('Failed to generate sitemap: ' . $e->getMessage());
                // Fallback to basic sitemap
                $sitemap = $this->generateBasicSitemap();
            }

            // Ensure we have valid XML content
            if (empty($sitemap) || !str_contains($sitemap, '<urlset')) {
                $sitemap = $this->generateBasicSitemap();
            }

            return Response::make($sitemap, 200, [
                'Content-Type' => 'application/xml; charset=UTF-8',
                'Cache-Control' => 'public, max-age=' . ($cacheDuration * 60),
                'X-Robots-Tag' => 'noindex',
            ]);

        } catch (\Exception $e) {
            \Log::error('Critical error in sitemap generation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            // Return a minimal sitemap as last resort
            $basicSitemap = $this->generateBasicSitemap();
            return Response::make($basicSitemap, 200, [
                'Content-Type' => 'application/xml; charset=UTF-8',
                'Cache-Control' => 'no-cache',
            ]);
        }
    }

    private function generateSitemap()
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<?xml-stylesheet type="text/xsl" href="' . url('/sitemap.xsl') . '"?>' . PHP_EOL;
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"' . PHP_EOL;
        $xml .= '        xmlns:xhtml="http://www.w3.org/1999/xhtml"' . PHP_EOL;
        $xml .= '        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">' . PHP_EOL;
        $xml .= '  <!-- Generated by ' . config('app.name', 'Laravel') . ' on ' . now()->format('Y-m-d H:i:s T') . ' -->' . PHP_EOL;

        // Add homepage with enhanced metadata
        $xml .= $this->addUrl(url('/'), now(), 'daily', '1.0', [], true);

        // Add static pages
        if (!function_exists('getSetting') || getSetting('sitemap_include_pages', true)) {
            $xml .= $this->addPages();
        }

        // Add blog posts and index
        if ((!function_exists('getSetting') || getSetting('sitemap_include_blog', true)) &&
            (!function_exists('getSetting') || getSetting('enable_blog', true))) {
            $xml .= $this->addBlogPosts();
            $xml .= $this->addBlogIndex();
        }

        $xml .= '</urlset>' . PHP_EOL;

        return $xml;
    }

    private function addUrl($url, $lastmod = null, $changefreq = 'weekly', $priority = '0.5', $alternates = [], $isHomepage = false)
    {
        // Ensure URL is properly formatted and canonical
        $canonicalUrl = $this->canonicalizeUrl($url);

        $xml = '  <url>' . PHP_EOL;
        $xml .= '    <loc>' . htmlspecialchars($canonicalUrl) . '</loc>' . PHP_EOL;

        if ($lastmod) {
            $xml .= '    <lastmod>' . $lastmod->format('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
        }

        $xml .= '    <changefreq>' . htmlspecialchars($changefreq) . '</changefreq>' . PHP_EOL;
        $xml .= '    <priority>' . number_format((float)$priority, 1) . '</priority>' . PHP_EOL;

        // Add hreflang alternates for multilingual support
        if (!empty($alternates)) {
            foreach ($alternates as $lang => $altUrl) {
                $canonicalAltUrl = $this->canonicalizeUrl($altUrl);
                $xml .= '    <xhtml:link rel="alternate" hreflang="' . htmlspecialchars($lang) . '" href="' . htmlspecialchars($canonicalAltUrl) . '" />' . PHP_EOL;
            }

            // Add x-default for homepage or main language
            if ($isHomepage && !empty($alternates)) {
                $defaultUrl = $this->canonicalizeUrl($url);
                $xml .= '    <xhtml:link rel="alternate" hreflang="x-default" href="' . htmlspecialchars($defaultUrl) . '" />' . PHP_EOL;
            }
        }

        $xml .= '  </url>' . PHP_EOL;

        return $xml;
    }

    private function canonicalizeUrl($url)
    {
        // Ensure URL is properly formatted
        $url = trim($url);

        // Remove trailing slash except for homepage
        if ($url !== url('/') && str_ends_with($url, '/')) {
            $url = rtrim($url, '/');
        }

        // Ensure HTTPS if forced
        if (config('app.force_https', false) || (function_exists('getSetting') && getSetting('https_force', false))) {
            $url = str_replace('http://', 'https://', $url);
        }

        return $url;
    }

    private function addPages()
    {
        $xml = '';
        try {
            $pages = Page::where('status', 1)->get();
            $priority = function_exists('getSetting') ? getSetting('sitemap_pages_priority', '0.8') : '0.8';
            $changefreq = function_exists('getSetting') ? getSetting('sitemap_pages_changefreq', 'weekly') : 'weekly';

            foreach ($pages as $page) {
                try {
                    $url = route('page', $page->slug);
                    $alternates = $this->getPageAlternates($page);
                    $xml .= $this->addUrl($url, $page->updated_at, $changefreq, $priority, $alternates);
                } catch (\Exception $e) {
                    // Skip this page if route generation fails
                    \Log::warning('Failed to generate sitemap URL for page: ' . $page->slug, ['error' => $e->getMessage()]);
                    continue;
                }
            }
        } catch (\Exception $e) {
            // If database is not available, add sample pages
            \Log::warning('Failed to load pages for sitemap, using sample data: ' . $e->getMessage());
            $xml .= $this->addSamplePages();
        }

        return $xml;
    }

    private function addBlogPosts()
    {
        $xml = '';
        try {
            $posts = BlogPost::where('status', 1)->get();
            $priority = function_exists('getSetting') ? getSetting('sitemap_blog_priority', '0.7') : '0.7';
            $changefreq = function_exists('getSetting') ? getSetting('sitemap_blog_changefreq', 'weekly') : 'weekly';

            foreach ($posts as $post) {
                try {
                    $url = route('posts', $post->slug);
                    $alternates = $this->getBlogPostAlternates($post);
                    $xml .= $this->addUrl($url, $post->updated_at, $changefreq, $priority, $alternates);
                } catch (\Exception $e) {
                    // Skip this post if route generation fails
                    \Log::warning('Failed to generate sitemap URL for blog post: ' . $post->slug, ['error' => $e->getMessage()]);
                    continue;
                }
            }
        } catch (\Exception $e) {
            // If database is not available, add sample blog posts
            \Log::warning('Failed to load blog posts for sitemap, using sample data: ' . $e->getMessage());
            $xml .= $this->addSampleBlogPosts();
        }

        return $xml;
    }

    private function addBlogIndex()
    {
        $xml = '';
        try {
            // Check if blog route exists
            if (!\Route::has('blog')) {
                return '';
            }

            $url = route('blog');
            $priority = function_exists('getSetting') ? getSetting('sitemap_blog_priority', '0.7') : '0.7';
            $changefreq = function_exists('getSetting') ? getSetting('sitemap_blog_changefreq', 'weekly') : 'weekly';
            $lastPost = BlogPost::where('status', 1)->latest('updated_at')->first();
            $lastmod = $lastPost ? $lastPost->updated_at : now();

            $alternates = $this->getBlogIndexAlternates();
            $xml .= $this->addUrl($url, $lastmod, $changefreq, $priority, $alternates);
        } catch (\Exception $e) {
            \Log::error('Failed to add blog index to sitemap: ' . $e->getMessage());
            return '';
        }

        return $xml;
    }

    private function getPageAlternates($page)
    {
        $alternates = [];
        try {
            if (!class_exists('App\Models\Language')) {
                return $alternates;
            }

            $languages = Language::where('status', 1)->get();

            foreach ($languages as $language) {
                try {
                    $altPage = Page::where('slug', $page->slug)
                        ->where('lang', $language->code)
                        ->where('status', 1)
                        ->first();

                    if ($altPage && \Route::has('page')) {
                        $currentLang = function_exists('getCurrentLang') ? getCurrentLang() : 'en';
                        $altUrl = str_replace('/' . $currentLang . '/', '/' . $language->code . '/', route('page', $altPage->slug));
                        $alternates[$language->code] = $altUrl;
                    }
                } catch (\Exception $e) {
                    // Skip this language if there's an error
                    continue;
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to get page alternates: ' . $e->getMessage());
        }

        return $alternates;
    }

    private function getBlogPostAlternates($post)
    {
        $alternates = [];
        try {
            if (!class_exists('App\Models\Language')) {
                return $alternates;
            }

            $languages = Language::where('status', 1)->get();

            foreach ($languages as $language) {
                try {
                    $altPost = BlogPost::where('slug', $post->slug)
                        ->where('lang', $language->code)
                        ->where('status', 1)
                        ->first();

                    if ($altPost && \Route::has('posts')) {
                        $currentLang = function_exists('getCurrentLang') ? getCurrentLang() : 'en';
                        $altUrl = str_replace('/' . $currentLang . '/', '/' . $language->code . '/', route('posts', $altPost->slug));
                        $alternates[$language->code] = $altUrl;
                    }
                } catch (\Exception $e) {
                    // Skip this language if there's an error
                    continue;
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to get blog post alternates: ' . $e->getMessage());
        }

        return $alternates;
    }

    private function getBlogIndexAlternates()
    {
        $alternates = [];
        try {
            if (!class_exists('App\Models\Language') || !\Route::has('blog')) {
                return $alternates;
            }

            $languages = Language::where('status', 1)->get();

            foreach ($languages as $language) {
                try {
                    $currentLang = function_exists('getCurrentLang') ? getCurrentLang() : 'en';
                    $altUrl = str_replace('/' . $currentLang . '/', '/' . $language->code . '/', route('blog'));
                    $alternates[$language->code] = $altUrl;
                } catch (\Exception $e) {
                    // Skip this language if there's an error
                    continue;
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to get blog index alternates: ' . $e->getMessage());
        }

        return $alternates;
    }

    private function isDatabaseAvailable()
    {
        try {
            \DB::connection()->getPdo();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    private function generateBasicSitemap()
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<?xml-stylesheet type="text/xsl" href="' . url('/sitemap.xsl') . '"?>' . PHP_EOL;
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"' . PHP_EOL;
        $xml .= '        xmlns:xhtml="http://www.w3.org/1999/xhtml"' . PHP_EOL;
        $xml .= '        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">' . PHP_EOL;
        $xml .= '  <!-- Generated by ' . config('app.name', 'Laravel') . ' on ' . now()->format('Y-m-d H:i:s T') . ' -->' . PHP_EOL;

        // Add homepage
        $xml .= '  <url>' . PHP_EOL;
        $xml .= '    <loc>' . htmlspecialchars(url('/')) . '</loc>' . PHP_EOL;
        $xml .= '    <lastmod>' . now()->format('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
        $xml .= '    <changefreq>daily</changefreq>' . PHP_EOL;
        $xml .= '    <priority>1.0</priority>' . PHP_EOL;
        $xml .= '  </url>' . PHP_EOL;

        $xml .= '</urlset>' . PHP_EOL;

        return $xml;
    }

    /**
     * Add sample pages when database is not available
     */
    private function addSamplePages()
    {
        $xml = '';
        $priority = function_exists('getSetting') ? getSetting('sitemap_pages_priority', '0.8') : '0.8';
        $changefreq = function_exists('getSetting') ? getSetting('sitemap_pages_changefreq', 'weekly') : 'weekly';

        $samplePages = [
            ['slug' => 'privacy-policy', 'title' => 'Privacy Policy'],
            ['slug' => 'terms-of-service', 'title' => 'Terms of Service'],
            ['slug' => 'about-us', 'title' => 'About Us'],
            ['slug' => 'contact', 'title' => 'Contact'],
            ['slug' => 'faq', 'title' => 'Frequently Asked Questions'],
            ['slug' => 'how-it-works', 'title' => 'How It Works'],
        ];

        foreach ($samplePages as $page) {
            try {
                $url = url('/' . $page['slug']);
                $xml .= $this->addUrl($url, now()->subDays(rand(1, 30)), $changefreq, $priority);
            } catch (\Exception $e) {
                // Skip this page if URL generation fails
                continue;
            }
        }

        return $xml;
    }

    /**
     * Add sample blog posts when database is not available
     */
    private function addSampleBlogPosts()
    {
        $xml = '';
        $priority = function_exists('getSetting') ? getSetting('sitemap_blog_priority', '0.7') : '0.7';
        $changefreq = function_exists('getSetting') ? getSetting('sitemap_blog_changefreq', 'weekly') : 'weekly';

        $samplePosts = [
            ['slug' => 'ultimate-guide-temporary-email', 'title' => 'The Ultimate Guide to Temporary Email Services'],
            ['slug' => 'protect-privacy-online', 'title' => 'How to Protect Your Privacy Online'],
            ['slug' => 'avoid-spam-emails', 'title' => '10 Ways to Avoid Spam Emails'],
            ['slug' => 'temporary-email-vs-regular-email', 'title' => 'Temporary Email vs Regular Email: Which is Better?'],
            ['slug' => 'email-security-best-practices', 'title' => 'Email Security Best Practices for 2025'],
        ];

        foreach ($samplePosts as $post) {
            try {
                $url = url('/blog/' . $post['slug']);
                $xml .= $this->addUrl($url, now()->subDays(rand(1, 60)), $changefreq, $priority);
            } catch (\Exception $e) {
                // Skip this post if URL generation fails
                continue;
            }
        }

        return $xml;
    }
}
