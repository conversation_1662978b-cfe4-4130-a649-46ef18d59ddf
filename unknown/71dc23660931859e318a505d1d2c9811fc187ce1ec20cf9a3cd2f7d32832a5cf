{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "anhskohbo/no-captcha": "^3.6", "artesaos/seotools": "^1.3", "artisync/image": "^9.3", "cviebrock/eloquent-sluggable": "^11.0", "ezyang/htmlpurifier": "^4.17", "intervention/image-laravel": "1.2", "irazasyed/telegram-bot-sdk": "^3.14", "laravel/framework": "^11.9", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.15", "laravel/tinker": "^2.9", "laravel/ui": "^4.5", "mcamara/laravel-localization": "^2.0", "php-flasher/flasher-notyf-laravel": "1.15", "php-mime-mail-parser/php-mime-mail-parser": "^1.0", "teampanfu/laravel-hcaptcha": "^1.1", "trustip/trustip": "^1.1", "vinkla/hashids": "^12.0", "webklex/laravel-imap": "^5.3.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Lobage\\Planify\\": "vendor/lobage/planify/src/"}, "files": ["app/Helpers/Helper.php", "app/Helpers/UploadFilesHelper.php", "app/Helpers/SubscriptionHelper.php", "app/Helpers/LanguageHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "time": "2025-02-26 00:52:01"}