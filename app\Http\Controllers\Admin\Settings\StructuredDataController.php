<?php

namespace App\Http\Controllers\Admin\Settings;

use App\Models\Setting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateStructuredDataRequest;

class StructuredDataController extends Controller
{
    public function index()
    {
        $settings = [
            // Core structured data settings
            'structured_data_enabled' => getSetting('structured_data_enabled', true),
            'structured_data_organization' => getSetting('structured_data_organization', true),
            'structured_data_website' => getSetting('structured_data_website', true),
            'structured_data_breadcrumbs' => getSetting('structured_data_breadcrumbs', true),
            'structured_data_articles' => getSetting('structured_data_articles', true),

            // New schema types for temporary email service
            'structured_data_service' => getSetting('structured_data_service', true),
            'structured_data_software' => getSetting('structured_data_software', true),
            'structured_data_faq' => getSetting('structured_data_faq', true),
            'structured_data_localbusiness' => getSetting('structured_data_localbusiness', false),
            'structured_data_product' => getSetting('structured_data_product', true),
            'structured_data_howto' => getSetting('structured_data_howto', true),

            // Organization settings
            'organization_name' => getSetting('organization_name', getSetting('site_name', 'TrashMail Pro')),
            'organization_logo' => getSetting('organization_logo', getSetting('logo')),
            'organization_url' => getSetting('organization_url', url('/')),
            'organization_description' => getSetting('organization_description', 'Leading provider of secure temporary email services for privacy protection and spam prevention. Trusted by millions of users worldwide.'),
            'organization_slogan' => getSetting('organization_slogan', 'Your Privacy, Our Priority'),
            'organization_founding_date' => getSetting('organization_founding_date', date('Y')),
            'organization_contact_type' => getSetting('organization_contact_type', 'customer service'),
            'organization_telephone' => getSetting('organization_telephone', '******-PRIVACY'),
            'organization_email' => getSetting('organization_email', '<EMAIL>'),

            // Address settings
            'organization_address_street' => getSetting('organization_address_street', '123 Privacy Street'),
            'organization_address_city' => getSetting('organization_address_city', 'Secure City'),
            'organization_address_region' => getSetting('organization_address_region', 'Privacy State'),
            'organization_address_postal' => getSetting('organization_address_postal', '12345'),
            'organization_address_country' => getSetting('organization_address_country', 'United States'),

            // Social media settings
            'social_media_facebook' => getSetting('social_media_facebook', 'https://facebook.com/trashmailpro'),
            'social_media_twitter' => getSetting('social_media_twitter', 'https://twitter.com/trashmailpro'),
            'social_media_instagram' => getSetting('social_media_instagram', 'https://instagram.com/trashmailpro'),
            'social_media_linkedin' => getSetting('social_media_linkedin', 'https://linkedin.com/company/trashmailpro'),
            'social_media_youtube' => getSetting('social_media_youtube', 'https://youtube.com/@trashmailpro'),

            // Service-specific settings
            'service_name' => getSetting('service_name', getSetting('site_name', 'TrashMail Pro Service')),
            'service_description' => getSetting('service_description', 'Professional temporary email service providing secure, anonymous email addresses for privacy protection and spam prevention. Features include real-time email reception, multiple domain support, and automatic deletion.'),
            'service_area_served' => getSetting('service_area_served', 'Worldwide'),
            'service_feature_privacy' => getSetting('service_feature_privacy', true),
            'service_feature_temporary' => getSetting('service_feature_temporary', true),
            'service_feature_anonymous' => getSetting('service_feature_anonymous', true),
            'service_feature_spam_protection' => getSetting('service_feature_spam_protection', true),

            // Software application settings
            'app_name' => getSetting('app_name', getSetting('site_name', 'TrashMail Pro App')),
            'app_description' => getSetting('app_description', 'Advanced web-based temporary email application providing secure, anonymous email addresses with real-time reception, multi-domain support, and privacy-focused features for safe online communication.'),
            'app_version' => getSetting('app_version', '2.1.0'),
            'app_feature_realtime' => getSetting('app_feature_realtime', true),
            'app_feature_multiple_domains' => getSetting('app_feature_multiple_domains', true),
            'app_feature_auto_delete' => getSetting('app_feature_auto_delete', true),
            'app_feature_mobile_friendly' => getSetting('app_feature_mobile_friendly', true),

            // Website settings
            'site_alternate_name' => getSetting('site_alternate_name', getSetting('site_name', 'TrashMail Pro - Temporary Email Service')),
            'site_description' => getSetting('site_description', 'Professional temporary email service offering secure, anonymous email addresses for privacy protection, spam prevention, and safe online registration. No signup required.'),
            'enable_domain_search' => getSetting('enable_domain_search', true),
        ];

        return view('admin.settings.structured-data.index', compact('settings'));
    }

    public function update(UpdateStructuredDataRequest $request)
    {

        $settings = [
            // Core structured data settings
            'structured_data_enabled',
            'structured_data_organization',
            'structured_data_website',
            'structured_data_breadcrumbs',
            'structured_data_articles',
            'structured_data_service',
            'structured_data_software',
            'structured_data_faq',
            'structured_data_localbusiness',
            'structured_data_product',
            'structured_data_howto',

            // Organization settings
            'organization_name',
            'organization_logo',
            'organization_url',
            'organization_description',
            'organization_slogan',
            'organization_founding_date',
            'organization_contact_type',
            'organization_telephone',
            'organization_email',
            'organization_address_street',
            'organization_address_city',
            'organization_address_region',
            'organization_address_postal',
            'organization_address_country',

            // Social media settings
            'social_media_facebook',
            'social_media_twitter',
            'social_media_instagram',
            'social_media_linkedin',
            'social_media_youtube',

            // Service-specific settings
            'service_name',
            'service_description',
            'service_area_served',
            'service_feature_privacy',
            'service_feature_temporary',
            'service_feature_anonymous',
            'service_feature_spam_protection',

            // Software application settings
            'app_name',
            'app_description',
            'app_version',
            'app_feature_realtime',
            'app_feature_multiple_domains',
            'app_feature_auto_delete',
            'app_feature_mobile_friendly',

            // Website settings
            'site_alternate_name',
            'site_description',
            'enable_domain_search',
        ];

        foreach ($settings as $setting) {
            setSetting($setting, $request->$setting);
        }

        showToastr(__('lobage.toastr.update'));
        return back();
    }

    public function preview()
    {
        try {
            $structuredDataService = new \App\Services\StructuredDataService();
            $structuredDataHtml = $structuredDataService->generateStructuredData('general');

            // Extract JSON from HTML script tags
            $schemas = [];
            if (preg_match_all('/<script type="application\/ld\+json">(.*?)<\/script>/s', $structuredDataHtml, $matches)) {
                foreach ($matches[1] as $jsonString) {
                    $schema = json_decode(trim($jsonString), true);
                    if ($schema) {
                        $schemas[] = $schema;
                    }
                }
            }

            return response()->json($schemas, 200, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        } catch (\Exception $e) {
            \Log::error('Structured Data Preview Error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to generate structured data preview',
                'message' => $e->getMessage()
            ], 500);
        }
    }


}
