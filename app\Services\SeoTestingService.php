<?php

namespace App\Services;

use App\Models\Page;
use App\Models\BlogPost;
use Illuminate\Support\Facades\Http;

class SeoTestingService
{
    public function validateStructuredData($url = null)
    {
        $url = $url ?: url('/');
        $results = [];

        try {
            // Test with Google's Rich Results Test API (if available)
            $response = Http::timeout(10)->get('https://search.google.com/test/rich-results', [
                'url' => $url
            ]);

            $results['google_rich_results'] = [
                'status' => $response->successful() ? 'success' : 'error',
                'message' => $response->successful() ? 'URL submitted for testing' : 'Failed to submit to Google Rich Results Test'
            ];
        } catch (\Exception $e) {
            $results['google_rich_results'] = [
                'status' => 'error',
                'message' => 'Could not connect to Google Rich Results Test: ' . $e->getMessage()
            ];
        }

        // Validate JSON-LD syntax
        $structuredDataService = new StructuredDataService();
        $jsonLd = $structuredDataService->generateStructuredData('general');
        
        if ($jsonLd) {
            $results['json_ld_syntax'] = $this->validateJsonLdSyntax($jsonLd);
        } else {
            $results['json_ld_syntax'] = [
                'status' => 'warning',
                'message' => 'No structured data found'
            ];
        }

        return $results;
    }

    public function validateSitemap()
    {
        $results = [];

        try {
            $response = Http::timeout(10)->get(url('/sitemap.xml'));
            
            if ($response->successful()) {
                $xml = $response->body();
                $results['sitemap_accessibility'] = [
                    'status' => 'success',
                    'message' => 'Sitemap is accessible'
                ];

                // Validate XML syntax
                $results['sitemap_syntax'] = $this->validateXmlSyntax($xml);
                
                // Count URLs
                $urlCount = substr_count($xml, '<url>');
                $results['sitemap_urls'] = [
                    'status' => 'info',
                    'message' => "Sitemap contains {$urlCount} URLs"
                ];

                // Check for required elements
                $results['sitemap_structure'] = $this->validateSitemapStructure($xml);
            } else {
                $results['sitemap_accessibility'] = [
                    'status' => 'error',
                    'message' => 'Sitemap is not accessible (HTTP ' . $response->status() . ')'
                ];
            }
        } catch (\Exception $e) {
            $results['sitemap_accessibility'] = [
                'status' => 'error',
                'message' => 'Failed to access sitemap: ' . $e->getMessage()
            ];
        }

        return $results;
    }

    public function validateRobotsTxt()
    {
        $results = [];

        try {
            $response = Http::timeout(10)->get(url('/robots.txt'));
            
            if ($response->successful()) {
                $content = $response->body();
                $results['robots_accessibility'] = [
                    'status' => 'success',
                    'message' => 'Robots.txt is accessible'
                ];

                // Validate syntax
                $results['robots_syntax'] = $this->validateRobotsTxtSyntax($content);
                
                // Check for sitemap reference
                if (stripos($content, 'sitemap:') !== false) {
                    $results['robots_sitemap'] = [
                        'status' => 'success',
                        'message' => 'Sitemap reference found in robots.txt'
                    ];
                } else {
                    $results['robots_sitemap'] = [
                        'status' => 'warning',
                        'message' => 'No sitemap reference found in robots.txt'
                    ];
                }
            } else {
                $results['robots_accessibility'] = [
                    'status' => 'error',
                    'message' => 'Robots.txt is not accessible (HTTP ' . $response->status() . ')'
                ];
            }
        } catch (\Exception $e) {
            $results['robots_accessibility'] = [
                'status' => 'error',
                'message' => 'Failed to access robots.txt: ' . $e->getMessage()
            ];
        }

        return $results;
    }

    public function validateMetaTags($url = null)
    {
        $url = $url ?: url('/');
        $results = [];

        try {
            $response = Http::timeout(10)->get($url);
            
            if ($response->successful()) {
                $html = $response->body();
                
                // Check for essential meta tags
                $results['meta_title'] = $this->checkMetaTag($html, 'title') ? 
                    ['status' => 'success', 'message' => 'Title tag found'] :
                    ['status' => 'error', 'message' => 'Title tag missing'];

                $results['meta_description'] = $this->checkMetaTag($html, 'description') ? 
                    ['status' => 'success', 'message' => 'Meta description found'] :
                    ['status' => 'warning', 'message' => 'Meta description missing'];

                $results['meta_canonical'] = $this->checkMetaTag($html, 'canonical') ? 
                    ['status' => 'success', 'message' => 'Canonical URL found'] :
                    ['status' => 'warning', 'message' => 'Canonical URL missing'];

                $results['meta_og'] = $this->checkOpenGraphTags($html);
                $results['meta_twitter'] = $this->checkTwitterCardTags($html);
            } else {
                $results['page_accessibility'] = [
                    'status' => 'error',
                    'message' => 'Page is not accessible (HTTP ' . $response->status() . ')'
                ];
            }
        } catch (\Exception $e) {
            $results['page_accessibility'] = [
                'status' => 'error',
                'message' => 'Failed to access page: ' . $e->getMessage()
            ];
        }

        return $results;
    }

    private function validateJsonLdSyntax($jsonLd)
    {
        // Extract JSON from script tags
        preg_match_all('/<script type="application\/ld\+json">(.*?)<\/script>/s', $jsonLd, $matches);
        
        $errors = [];
        foreach ($matches[1] as $index => $json) {
            $decoded = json_decode(trim($json), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errors[] = "JSON-LD block " . ($index + 1) . ": " . json_last_error_msg();
            }
        }

        if (empty($errors)) {
            return [
                'status' => 'success',
                'message' => 'All JSON-LD blocks are valid'
            ];
        } else {
            return [
                'status' => 'error',
                'message' => 'JSON-LD syntax errors: ' . implode(', ', $errors)
            ];
        }
    }

    private function validateXmlSyntax($xml)
    {
        libxml_use_internal_errors(true);
        $doc = simplexml_load_string($xml);
        
        if ($doc === false) {
            $errors = libxml_get_errors();
            $errorMessages = array_map(function($error) {
                return trim($error->message);
            }, $errors);
            
            return [
                'status' => 'error',
                'message' => 'XML syntax errors: ' . implode(', ', $errorMessages)
            ];
        }

        return [
            'status' => 'success',
            'message' => 'XML syntax is valid'
        ];
    }

    private function validateSitemapStructure($xml)
    {
        $requiredElements = ['<urlset', '<url>', '<loc>'];
        $missing = [];

        foreach ($requiredElements as $element) {
            if (strpos($xml, $element) === false) {
                $missing[] = $element;
            }
        }

        if (empty($missing)) {
            return [
                'status' => 'success',
                'message' => 'Sitemap structure is valid'
            ];
        } else {
            return [
                'status' => 'error',
                'message' => 'Missing required elements: ' . implode(', ', $missing)
            ];
        }
    }

    private function validateRobotsTxtSyntax($content)
    {
        $lines = explode("\n", $content);
        $errors = [];
        $validDirectives = ['user-agent', 'disallow', 'allow', 'crawl-delay', 'sitemap', 'host'];

        foreach ($lines as $lineNumber => $line) {
            $line = trim($line);
            
            if (empty($line) || strpos($line, '#') === 0) {
                continue;
            }
            
            if (strpos($line, ':') === false) {
                $errors[] = "Line " . ($lineNumber + 1) . ": Missing colon";
                continue;
            }
            
            $parts = explode(':', $line, 2);
            $directive = strtolower(trim($parts[0]));
            
            if (!in_array($directive, $validDirectives)) {
                $errors[] = "Line " . ($lineNumber + 1) . ": Invalid directive '$directive'";
            }
        }

        if (empty($errors)) {
            return [
                'status' => 'success',
                'message' => 'Robots.txt syntax is valid'
            ];
        } else {
            return [
                'status' => 'error',
                'message' => 'Syntax errors: ' . implode(', ', $errors)
            ];
        }
    }

    private function checkMetaTag($html, $type)
    {
        switch ($type) {
            case 'title':
                return preg_match('/<title[^>]*>.*?<\/title>/i', $html);
            case 'description':
                return preg_match('/<meta[^>]*name=["\']description["\'][^>]*>/i', $html);
            case 'canonical':
                return preg_match('/<link[^>]*rel=["\']canonical["\'][^>]*>/i', $html);
            default:
                return false;
        }
    }

    private function checkOpenGraphTags($html)
    {
        $ogTags = ['og:title', 'og:description', 'og:url', 'og:type'];
        $found = 0;

        foreach ($ogTags as $tag) {
            if (preg_match('/<meta[^>]*property=["\']' . preg_quote($tag) . '["\'][^>]*>/i', $html)) {
                $found++;
            }
        }

        if ($found >= 3) {
            return ['status' => 'success', 'message' => "Found {$found}/4 essential OpenGraph tags"];
        } elseif ($found > 0) {
            return ['status' => 'warning', 'message' => "Found {$found}/4 essential OpenGraph tags"];
        } else {
            return ['status' => 'error', 'message' => 'No OpenGraph tags found'];
        }
    }

    private function checkTwitterCardTags($html)
    {
        $twitterTags = ['twitter:card', 'twitter:title', 'twitter:description'];
        $found = 0;

        foreach ($twitterTags as $tag) {
            if (preg_match('/<meta[^>]*name=["\']' . preg_quote($tag) . '["\'][^>]*>/i', $html)) {
                $found++;
            }
        }

        if ($found >= 2) {
            return ['status' => 'success', 'message' => "Found {$found}/3 essential Twitter Card tags"];
        } elseif ($found > 0) {
            return ['status' => 'warning', 'message' => "Found {$found}/3 essential Twitter Card tags"];
        } else {
            return ['status' => 'warning', 'message' => 'No Twitter Card tags found'];
        }
    }
}
