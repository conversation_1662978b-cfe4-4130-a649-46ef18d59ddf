<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRobotsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'robots_content' => [
                'required',
                'string',
                'max:10000',
                function ($attribute, $value, $fail) {
                    // Validate robots.txt syntax
                    $lines = explode("\n", $value);
                    $validDirectives = ['user-agent', 'disallow', 'allow', 'crawl-delay', 'sitemap', 'host'];
                    
                    foreach ($lines as $lineNumber => $line) {
                        $line = trim($line);
                        
                        // Skip empty lines and comments
                        if (empty($line) || strpos($line, '#') === 0) {
                            continue;
                        }
                        
                        // Check if line contains a colon
                        if (strpos($line, ':') === false) {
                            $fail("Line " . ($lineNumber + 1) . " is invalid. Each directive must contain a colon (:).");
                            return;
                        }
                        
                        // Extract directive name
                        $parts = explode(':', $line, 2);
                        $directive = strtolower(trim($parts[0]));
                        
                        // Validate directive
                        if (!in_array($directive, $validDirectives)) {
                            $fail("Line " . ($lineNumber + 1) . " contains an invalid directive: '$directive'. Valid directives are: " . implode(', ', $validDirectives));
                            return;
                        }
                        
                        // Validate specific directives
                        $value = trim($parts[1]);
                        
                        if ($directive === 'crawl-delay' && !is_numeric($value)) {
                            $fail("Line " . ($lineNumber + 1) . ": Crawl-delay value must be numeric.");
                            return;
                        }
                        
                        if ($directive === 'sitemap' && !filter_var($value, FILTER_VALIDATE_URL)) {
                            $fail("Line " . ($lineNumber + 1) . ": Sitemap value must be a valid URL.");
                            return;
                        }
                    }
                }
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'robots_content.required' => 'Robots.txt content is required.',
            'robots_content.string' => 'Robots.txt content must be a string.',
            'robots_content.max' => 'Robots.txt content cannot exceed 10,000 characters.',
        ];
    }
}
