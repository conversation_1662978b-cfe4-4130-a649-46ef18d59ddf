<?php

namespace App\Http\Controllers\Admin\Settings;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\SeoTestingService;

class SeoTestController extends Controller
{
    protected $seoTestingService;

    public function __construct(SeoTestingService $seoTestingService)
    {
        $this->seoTestingService = $seoTestingService;
    }

    public function index()
    {
        return view('admin.settings.seo-test.index');
    }

    public function runTests(Request $request)
    {
        $request->validate([
            'test_url' => 'nullable|url',
            'tests' => 'required|array|min:1',
            'tests.*' => 'in:structured_data,sitemap,robots,meta_tags'
        ]);

        $url = $request->test_url ?: url('/');
        $selectedTests = $request->tests;
        $results = [];

        if (in_array('structured_data', $selectedTests)) {
            $results['structured_data'] = $this->seoTestingService->validateStructuredData($url);
        }

        if (in_array('sitemap', $selectedTests)) {
            $results['sitemap'] = $this->seoTestingService->validateSitemap();
        }

        if (in_array('robots', $selectedTests)) {
            $results['robots'] = $this->seoTestingService->validateRobotsTxt();
        }

        if (in_array('meta_tags', $selectedTests)) {
            $results['meta_tags'] = $this->seoTestingService->validateMetaTags($url);
        }

        return response()->json([
            'success' => true,
            'results' => $results,
            'tested_url' => $url,
            'timestamp' => now()->toISOString()
        ]);
    }

    public function quickTest()
    {
        $results = [
            'structured_data' => $this->seoTestingService->validateStructuredData(),
            'sitemap' => $this->seoTestingService->validateSitemap(),
            'robots' => $this->seoTestingService->validateRobotsTxt(),
            'meta_tags' => $this->seoTestingService->validateMetaTags()
        ];

        // Calculate overall score
        $totalTests = 0;
        $passedTests = 0;

        foreach ($results as $category => $tests) {
            foreach ($tests as $test => $result) {
                $totalTests++;
                if ($result['status'] === 'success') {
                    $passedTests++;
                }
            }
        }

        $score = $totalTests > 0 ? round(($passedTests / $totalTests) * 100) : 0;

        return response()->json([
            'success' => true,
            'results' => $results,
            'score' => $score,
            'passed_tests' => $passedTests,
            'total_tests' => $totalTests,
            'timestamp' => now()->toISOString()
        ]);
    }
}
