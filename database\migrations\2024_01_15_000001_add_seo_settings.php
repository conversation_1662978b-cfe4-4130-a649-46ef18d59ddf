<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add SEO-related settings to the settings table
        $seoSettings = [
            // Robots.txt settings
            [
                'key' => 'robots_txt_content',
                'value' => "User-agent: *\nAllow: /\n\nSitemap: " . url('/sitemap.xml'),
                'created_at' => now(),
                'updated_at' => now(),
            ],

            // Sitemap settings
            [
                'key' => 'sitemap_enabled',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'sitemap_include_pages',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'sitemap_include_blog',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'sitemap_pages_priority',
                'value' => '0.8',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'sitemap_blog_priority',
                'value' => '0.7',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'sitemap_pages_changefreq',
                'value' => 'weekly',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'sitemap_blog_changefreq',
                'value' => 'weekly',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'sitemap_cache_duration',
                'value' => '60',
                'created_at' => now(),
                'updated_at' => now(),
            ],

            // Structured Data settings
            [
                'key' => 'structured_data_enabled',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'structured_data_organization',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'structured_data_website',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'structured_data_breadcrumbs',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'structured_data_articles',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],

            // Organization information
            [
                'key' => 'organization_name',
                'value' => env('APP_NAME', 'Trashmail'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization_logo',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization_url',
                'value' => url('/'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization_description',
                'value' => 'Temporary email service for privacy and security',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization_contact_type',
                'value' => 'customer service',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization_telephone',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization_email',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],

            // Organization address
            [
                'key' => 'organization_address_street',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization_address_city',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization_address_region',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization_address_postal',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization_address_country',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],

            // Social media links
            [
                'key' => 'social_media_facebook',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'social_media_twitter',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'social_media_instagram',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'social_media_linkedin',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'social_media_youtube',
                'value' => '',
                'created_at' => now(),
                'updated_at' => now(),
            ],

            // Additional SEO settings
            [
                'key' => 'enable_breadcrumbs',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'enable_hreflang',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'enable_canonical_urls',
                'value' => '1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // Insert settings only if they don't already exist
        foreach ($seoSettings as $setting) {
            DB::table('settings')->updateOrInsert(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove SEO settings
        $seoKeys = [
            'robots_txt_content',
            'sitemap_enabled',
            'sitemap_include_pages',
            'sitemap_include_blog',
            'sitemap_pages_priority',
            'sitemap_blog_priority',
            'sitemap_pages_changefreq',
            'sitemap_blog_changefreq',
            'sitemap_cache_duration',
            'structured_data_enabled',
            'structured_data_organization',
            'structured_data_website',
            'structured_data_breadcrumbs',
            'structured_data_articles',
            'organization_name',
            'organization_logo',
            'organization_url',
            'organization_description',
            'organization_contact_type',
            'organization_telephone',
            'organization_email',
            'organization_address_street',
            'organization_address_city',
            'organization_address_region',
            'organization_address_postal',
            'organization_address_country',
            'social_media_facebook',
            'social_media_twitter',
            'social_media_instagram',
            'social_media_linkedin',
            'social_media_youtube',
            'enable_breadcrumbs',
            'enable_hreflang',
            'enable_canonical_urls',
        ];

        DB::table('settings')->whereIn('key', $seoKeys)->delete();
    }
};
