<?php

namespace App\Http\Controllers\Admin\Settings;

use App\Models\Setting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateRobotsRequest;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class RobotsController extends Controller
{
    public function index()
    {
        $robotsContent = getSetting('robots_txt_content', $this->getDefaultRobotsContent());
        return view('admin.settings.robots.index', compact('robotsContent'));
    }

    public function update(UpdateRobotsRequest $request)
    {

        // Save to database
        setSetting('robots_txt_content', $request->robots_content);

        // Generate robots.txt file in public directory
        $this->generateRobotsFile($request->robots_content);

        showToastr(__('lobage.toastr.update'));
        return back();
    }

    private function generateRobotsFile($content)
    {
        try {
            $robotsPath = public_path('robots.txt');
            File::put($robotsPath, $content);
        } catch (\Exception $e) {
            Log::error('Failed to generate robots.txt: ' . $e->getMessage());
            throw $e;
        }
    }

    private function getDefaultRobotsContent()
    {
        return "User-agent: *\nAllow: /\n\nSitemap: " . url('/sitemap.xml');
    }

    public function preview()
    {
        $robotsContent = getSetting('robots_txt_content', $this->getDefaultRobotsContent());
        return response($robotsContent, 200, [
            'Content-Type' => 'text/plain',
        ]);
    }
}