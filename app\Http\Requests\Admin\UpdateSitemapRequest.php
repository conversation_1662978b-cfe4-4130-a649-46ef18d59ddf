<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSitemapRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'sitemap_enabled' => 'required|boolean',
            'sitemap_include_pages' => 'required|boolean',
            'sitemap_include_blog' => 'required|boolean',
            'sitemap_pages_priority' => [
                'required',
                'numeric',
                'between:0,1',
                'regex:/^(0(\.\d{1,2})?|1(\.0{1,2})?)$/'
            ],
            'sitemap_blog_priority' => [
                'required',
                'numeric',
                'between:0,1',
                'regex:/^(0(\.\d{1,2})?|1(\.0{1,2})?)$/'
            ],
            'sitemap_pages_changefreq' => 'required|in:always,hourly,daily,weekly,monthly,yearly,never',
            'sitemap_blog_changefreq' => 'required|in:always,hourly,daily,weekly,monthly,yearly,never',
            'sitemap_cache_duration' => 'required|integer|min:1|max:1440',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'sitemap_pages_priority.regex' => 'Pages priority must be a decimal between 0.0 and 1.0 with up to 2 decimal places.',
            'sitemap_blog_priority.regex' => 'Blog priority must be a decimal between 0.0 and 1.0 with up to 2 decimal places.',
            'sitemap_pages_changefreq.in' => 'Pages change frequency must be one of: always, hourly, daily, weekly, monthly, yearly, never.',
            'sitemap_blog_changefreq.in' => 'Blog change frequency must be one of: always, hourly, daily, weekly, monthly, yearly, never.',
            'sitemap_cache_duration.min' => 'Cache duration must be at least 1 minute.',
            'sitemap_cache_duration.max' => 'Cache duration cannot exceed 1440 minutes (24 hours).',
        ];
    }
}
